// Client-side OpenRouter API integration for plant identification

export interface PlantIdentificationResult {
  name: string;
  scientificName: string;
  confidence: number;
  family?: string;
  description?: string;
  careInstructions?: {
    light: string;
    water: string;
    temperature: string;
    humidity?: string;
    soil?: string;
  };
}

export interface PlantDiagnosisResult {
  issue: string;
  severity: 'Low' | 'Medium' | 'High';
  advice: string;
  confidence: number;
}

export class OpenRouterError extends Error {
  constructor(message: string, public statusCode?: number) {
    super(message);
    this.name = 'OpenRouterError';
  }
}

// Convert image file to base64 data URL
export async function imageToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      if (typeof reader.result === 'string') {
        resolve(reader.result);
      } else {
        reject(new Error('Failed to convert image to base64'));
      }
    };
    reader.onerror = () => reject(new Error('Failed to read image file'));
    reader.readAsDataURL(file);
  });
}

// Make API call to OpenRouter via Next.js API route
async function callOpenRouterAPI(endpoint: string, data: any): Promise<any> {
  try {
    const response = await fetch(`/api/openrouter/${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new OpenRouterError(
        `API error: ${response.status} ${response.statusText} - ${errorText}`,
        response.status
      );
    }

    const result = await response.json();
    return result;
  } catch (error) {
    if (error instanceof OpenRouterError) {
      throw error;
    }
    throw new OpenRouterError(`Network error: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Identify a plant from an image
export async function identifyPlant(imageDataUrl: string): Promise<PlantIdentificationResult> {
  try {
    const result = await callOpenRouterAPI('identify', { imageDataUrl });
    return result;
  } catch (error) {
    if (error instanceof OpenRouterError) {
      throw error;
    }
    throw new OpenRouterError(`Failed to identify plant: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Diagnose plant health issues
export async function diagnosePlant(imageDataUrl: string, symptoms: string): Promise<PlantDiagnosisResult> {
  try {
    const result = await callOpenRouterAPI('diagnose', { imageDataUrl, symptoms });
    return result;
  } catch (error) {
    if (error instanceof OpenRouterError) {
      throw error;
    }
    throw new OpenRouterError(`Failed to diagnose plant: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Handle camera capture
export async function captureImage(): Promise<File | null> {
  return new Promise((resolve) => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.capture = 'environment'; // Use rear camera on mobile
    
    input.onchange = (event) => {
      const target = event.target as HTMLInputElement;
      const file = target.files?.[0];
      resolve(file || null);
    };
    
    input.oncancel = () => resolve(null);
    input.click();
  });
}

// Handle image upload from gallery
export async function uploadImage(): Promise<File | null> {
  return new Promise((resolve) => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    
    input.onchange = (event) => {
      const target = event.target as HTMLInputElement;
      const file = target.files?.[0];
      resolve(file || null);
    };
    
    input.oncancel = () => resolve(null);
    input.click();
  });
}
