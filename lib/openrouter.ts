// OpenRouter API integration for plant identification using Gemini 2.5 Flash Lite

export interface PlantIdentificationResult {
  name: string;
  scientificName: string;
  confidence: number;
  family?: string;
  description?: string;
  careInstructions?: {
    light: string;
    water: string;
    temperature: string;
    humidity?: string;
    soil?: string;
  };
}

export interface PlantDiagnosisResult {
  issue: string;
  severity: 'Low' | 'Medium' | 'High';
  advice: string;
  confidence: number;
}

export class OpenRouterError extends Error {
  constructor(message: string, public statusCode?: number) {
    super(message);
    this.name = 'OpenRouterError';
  }
}

// Convert image file to base64 data URL
export async function imageToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      if (typeof reader.result === 'string') {
        resolve(reader.result);
      } else {
        reject(new Error('Failed to convert image to base64'));
      }
    };
    reader.onerror = () => reject(new Error('Failed to read image file'));
    reader.readAsDataURL(file);
  });
}

// Make API call to OpenRouter
async function callOpenRouter(messages: any[]): Promise<any> {
  const apiKey = process.env.OPENROUTER_API_KEY || process.env.NEXT_PUBLIC_OPENROUTER_API_KEY;
  const apiUrl = process.env.NEXT_PUBLIC_OPENROUTER_API_URL || 'https://openrouter.ai/api/v1/chat/completions';

  if (!apiKey) {
    throw new OpenRouterError('OpenRouter API key not found in environment variables');
  }

  try {
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
      },
      body: JSON.stringify({
        model: 'google/gemini-2.5-flash-lite',
        messages,
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new OpenRouterError(
        `OpenRouter API error: ${response.status} ${response.statusText} - ${errorText}`,
        response.status
      );
    }

    const data = await response.json();
    return data;
  } catch (error) {
    if (error instanceof OpenRouterError) {
      throw error;
    }
    throw new OpenRouterError(`Network error: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Identify a plant from an image
export async function identifyPlant(imageDataUrl: string): Promise<PlantIdentificationResult> {
  const messages = [
    {
      role: 'user',
      content: [
        {
          type: 'text',
          text: `Please identify this plant and provide detailed information. Return your response in the following JSON format:
{
  "name": "Common name of the plant",
  "scientificName": "Scientific name",
  "confidence": 85,
  "family": "Plant family",
  "description": "Brief description of the plant",
  "careInstructions": {
    "light": "Light requirements (e.g., 'Bright, indirect sunlight')",
    "water": "Watering instructions (e.g., 'Water when top inch of soil is dry')",
    "temperature": "Temperature range (e.g., '65-85°F (18-29°C)')",
    "humidity": "Humidity requirements",
    "soil": "Soil requirements"
  }
}

Please be as accurate as possible and provide a confidence score from 0-100. If you cannot identify the plant with reasonable confidence, set confidence to a lower value and provide your best guess.`
        },
        {
          type: 'image_url',
          image_url: {
            url: imageDataUrl
          }
        }
      ]
    }
  ];

  try {
    const response = await callOpenRouter(messages);
    
    if (!response.choices || !response.choices[0] || !response.choices[0].message) {
      throw new OpenRouterError('Invalid response format from OpenRouter API');
    }

    const content = response.choices[0].message.content;
    
    // Try to extract JSON from the response
    let jsonMatch = content.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      // If no JSON found, try to parse the entire content
      jsonMatch = [content];
    }

    try {
      const result = JSON.parse(jsonMatch[0]);
      
      // Validate required fields
      if (!result.name || !result.scientificName || typeof result.confidence !== 'number') {
        throw new Error('Missing required fields in API response');
      }

      return {
        name: result.name,
        scientificName: result.scientificName,
        confidence: Math.max(0, Math.min(100, result.confidence)),
        family: result.family,
        description: result.description,
        careInstructions: result.careInstructions || {
          light: 'Bright, indirect light',
          water: 'Water when soil is dry',
          temperature: '65-75°F (18-24°C)'
        }
      };
    } catch (parseError) {
      // Fallback: create a result from the text response
      return {
        name: 'Unknown Plant',
        scientificName: 'Species unknown',
        confidence: 30,
        description: content.substring(0, 200) + '...',
        careInstructions: {
          light: 'Bright, indirect light',
          water: 'Water when soil is dry',
          temperature: '65-75°F (18-24°C)'
        }
      };
    }
  } catch (error) {
    if (error instanceof OpenRouterError) {
      throw error;
    }
    throw new OpenRouterError(`Failed to identify plant: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Diagnose plant health issues
export async function diagnosePlant(imageDataUrl: string, symptoms: string): Promise<PlantDiagnosisResult> {
  const messages = [
    {
      role: 'user',
      content: [
        {
          type: 'text',
          text: `Please diagnose this plant's health issues based on the image and symptoms described. 

Symptoms reported: "${symptoms}"

Return your response in the following JSON format:
{
  "issue": "Primary health issue identified",
  "severity": "Low|Medium|High",
  "advice": "Detailed care advice and treatment recommendations",
  "confidence": 85
}

Please provide specific, actionable advice for treating the identified issues.`
        },
        {
          type: 'image_url',
          image_url: {
            url: imageDataUrl
          }
        }
      ]
    }
  ];

  try {
    const response = await callOpenRouter(messages);
    
    if (!response.choices || !response.choices[0] || !response.choices[0].message) {
      throw new OpenRouterError('Invalid response format from OpenRouter API');
    }

    const content = response.choices[0].message.content;
    
    // Try to extract JSON from the response
    let jsonMatch = content.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      jsonMatch = [content];
    }

    try {
      const result = JSON.parse(jsonMatch[0]);
      
      return {
        issue: result.issue || 'Unknown issue',
        severity: ['Low', 'Medium', 'High'].includes(result.severity) ? result.severity : 'Medium',
        advice: result.advice || content.substring(0, 300) + '...',
        confidence: Math.max(0, Math.min(100, result.confidence || 50))
      };
    } catch (parseError) {
      // Fallback: create a result from the text response
      return {
        issue: 'Plant health assessment',
        severity: 'Medium' as const,
        advice: content.substring(0, 300) + '...',
        confidence: 40
      };
    }
  } catch (error) {
    if (error instanceof OpenRouterError) {
      throw error;
    }
    throw new OpenRouterError(`Failed to diagnose plant: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}
