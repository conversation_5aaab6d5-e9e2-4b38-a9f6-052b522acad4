import { NextRequest, NextResponse } from 'next/server';
import { diagnosePlant } from '@/lib/openrouter';

export async function POST(request: NextRequest) {
  try {
    const { imageDataUrl, symptoms } = await request.json();

    if (!imageDataUrl) {
      return NextResponse.json(
        { error: 'Image data URL is required' },
        { status: 400 }
      );
    }

    if (!symptoms) {
      return NextResponse.json(
        { error: 'Symptoms description is required' },
        { status: 400 }
      );
    }

    const result = await diagnosePlant(imageDataUrl, symptoms);
    return NextResponse.json(result);
  } catch (error) {
    console.error('Plant diagnosis error:', error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to diagnose plant' },
      { status: 500 }
    );
  }
}
