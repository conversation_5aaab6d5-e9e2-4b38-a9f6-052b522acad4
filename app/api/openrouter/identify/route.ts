import { NextRequest, NextResponse } from 'next/server';
import { identifyPlant } from '@/lib/openrouter';

export async function POST(request: NextRequest) {
  try {
    const { imageDataUrl } = await request.json();

    if (!imageDataUrl) {
      return NextResponse.json(
        { error: 'Image data URL is required' },
        { status: 400 }
      );
    }

    const result = await identifyPlant(imageDataUrl);
    return NextResponse.json(result);
  } catch (error) {
    console.error('Plant identification error:', error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to identify plant' },
      { status: 500 }
    );
  }
}
